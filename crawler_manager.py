"""
爬虫管理器 - 支持多种数据源的增量更新
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Type, Optional

from crawler.Spider_THS_724 import Spider_THS_724
from crawler.Spider_Sina_724 import Spider_Sina_724
from crawler.Spider_THS_finance import Spider_THS_Finance
from crawler.Spider_East_Money_ShangPin import Spider_East_Money_ShangPin
from crawler.Spider_East_Money_724 import Spider_East_Money_724
from crawler.BaseSpider import BaseSpider
from db_manager import DBManager
from article import Article

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CrawlerManager:
    """爬虫管理器，支持多种数据源的增量更新"""

    def __init__(self):
        self.db_manager = DBManager()
        # 注册所有可用的爬虫类
        self.spider_registry: Dict[str, Type[BaseSpider]] = {
            "同花顺_7*24": Spider_THS_724,
            "新浪财经_7*24": Spider_Sina_724,
            "同花顺_财经_财经要闻": Spider_THS_Finance,
            "东方财富_全球财经快讯_商品": Spider_East_Money_ShangPin,
            "东方财富_全球财经快讯_7*24": Spider_East_Money_724,
        }

    def get_available_sources(self) -> List[str]:
        """获取所有可用的数据源列表"""
        return list(self.spider_registry.keys())

    def get_latest_time_for_source(self, source_name: str) -> Optional[str]:
        """获取指定数据源的最新时间"""
        return self.db_manager.get_latest_time_by_source(source_name)

    def calculate_crawl_duration(self, latest_time: str) -> int:
        """
        计算需要爬取的时间范围（分钟）
        
        Args:
            latest_time: 最新时间字符串，格式为 "YYYY-MM-DD HH:MM:SS"
            
        Returns:
            需要爬取的分钟数
        """
        if not latest_time:
            # 如果没有历史数据，默认爬取最近24小时
            return 24 * 60

        try:
            latest_datetime = datetime.strptime(latest_time, "%Y-%m-%d %H:%M:%S")
            now = datetime.now()
            duration = now - latest_datetime
            # 转换为分钟，并添加一些缓冲时间
            minutes = int(duration.total_seconds() / 60) + 30
            return max(minutes, 60)  # 至少爬取1小时
        except ValueError as e:
            logger.error(f"时间格式解析错误: {latest_time}, {e}")
            return 24 * 60

    def crawl_incremental_data(self, source_name: str) -> Dict:
        """
        为指定数据源执行增量爬取

        Args:
            source_name: 数据源名称

        Returns:
            包含爬取结果的字典
        """
        result = {
            "success": False,
            "message": "",
            "crawled_count": 0,
            "inserted_count": 0,
            "latest_time": None,
            "duration_minutes": 0
        }

        try:
            # 检查数据源是否支持
            if source_name not in self.spider_registry:
                result["message"] = f"不支持的数据源: {source_name}"
                return result

            # 获取最新时间
            latest_time = self.get_latest_time_for_source(source_name)
            result["latest_time"] = latest_time

            # 计算爬取时间范围
            duration_minutes = self.calculate_crawl_duration(latest_time)
            result["duration_minutes"] = duration_minutes

            logger.info(f"开始增量爬取 {source_name}，最新时间: {latest_time}，爬取范围: {duration_minutes}分钟")

            # 创建爬虫实例并执行爬取
            spider_class = self.spider_registry[source_name]
            spider = spider_class(source_id=1, db_conn=None)  # 这里的source_id暂时用1

            # 执行简单的爬取（只爬取第一页，避免复杂的时间逻辑）
            crawled_articles = []

            # 爬取多页数据直到获取足够的历史数据
            max_pages = 10  # 最多爬取10页
            end_time = datetime.now() - timedelta(minutes=duration_minutes)

            for page in range(1, max_pages + 1):
                try:
                    page_url = spider._get_page_url(page)
                    html = spider.fetch(page_url)
                    page_articles = spider.parse(html)

                    if not page_articles:
                        break

                    # 检查是否已经爬取到足够旧的数据
                    oldest_article = page_articles[-1]
                    oldest_time = datetime.strptime(oldest_article.published_at, "%Y-%m-%d %H:%M:%S")

                    # 过滤出新于最新时间的文章
                    if latest_time:
                        latest_datetime = datetime.strptime(latest_time, "%Y-%m-%d %H:%M:%S")
                        new_articles = [a for a in page_articles if
                                        datetime.strptime(a.published_at, "%Y-%m-%d %H:%M:%S") > latest_datetime]
                        crawled_articles.extend(new_articles)
                    else:
                        # 如果没有历史数据，添加所有文章
                        crawled_articles.extend(page_articles)

                    # 如果最老的文章时间已经早于目标时间，停止爬取
                    if oldest_time < end_time:
                        break

                except Exception as e:
                    logger.error(f"爬取第{page}页时出错: {e}")
                    break

            result["crawled_count"] = len(crawled_articles)

            if not crawled_articles:
                result["message"] = "没有爬取到新数据"
                result["success"] = True
                return result

            # 将爬取结果插入数据库
            inserted_count = self.db_manager.batch_insert_articles_with_dedup(crawled_articles)
            result["inserted_count"] = inserted_count

            result["success"] = True
            result["message"] = f"成功爬取 {len(crawled_articles)} 条数据，新增 {inserted_count} 条"

            logger.info(f"增量爬取完成: {result['message']}")

        except Exception as e:
            error_msg = f"爬取过程中发生错误: {str(e)}"
            logger.error(error_msg, exc_info=True)
            result["message"] = error_msg

        return result

    def crawl_all_sources(self) -> Dict[str, Dict]:
        """
        为所有数据源执行增量爬取
        
        Returns:
            每个数据源的爬取结果字典
        """
        results = {}

        for source_name in self.get_available_sources():
            logger.info(f"开始处理数据源: {source_name}")
            results[source_name] = self.crawl_incremental_data(source_name)

        return results

    def register_spider(self, source_name: str, spider_class: Type[BaseSpider]):
        """
        注册新的爬虫类
        
        Args:
            source_name: 数据源名称
            spider_class: 爬虫类
        """
        self.spider_registry[source_name] = spider_class
        logger.info(f"已注册新的爬虫: {source_name}")


# 全局实例
crawler_manager = CrawlerManager()


def update_news_data_ui():
    """Streamlit UI组件：数据更新界面"""
    import streamlit as st

    st.subheader("📡 数据更新")

    # 显示可用数据源
    available_sources = crawler_manager.get_available_sources()

    col1, col2 = st.columns([2, 1])

    with col1:
        st.write("**可用数据源:**")
        for source in available_sources:
            latest_time = crawler_manager.get_latest_time_for_source(source)
            if latest_time:
                st.write(f"• {source}: 最新数据时间 {latest_time}")
            else:
                st.write(f"• {source}: 暂无数据")

    with col2:
        # 单个数据源更新
        selected_source = st.selectbox("选择数据源", available_sources)

        if st.button("🔄 更新选中数据源", key="update_single"):
            with st.spinner(f"正在更新 {selected_source} 数据..."):
                result = crawler_manager.crawl_incremental_data(selected_source)

                if result["success"]:
                    st.success(result["message"])
                    if result["inserted_count"] > 0:
                        st.balloons()
                else:
                    st.error(result["message"])

        # 全部数据源更新
        if st.button("🚀 更新所有数据源", key="update_all"):
            with st.spinner("正在更新所有数据源..."):
                results = crawler_manager.crawl_all_sources()

                success_count = 0
                total_inserted = 0

                for source_name, result in results.items():
                    if result["success"]:
                        success_count += 1
                        total_inserted += result["inserted_count"]
                        if result["inserted_count"] > 0:
                            st.success(f"{source_name}: {result['message']}")
                        else:
                            st.info(f"{source_name}: {result['message']}")
                    else:
                        st.error(f"{source_name}: {result['message']}")

                if success_count > 0:
                    st.success(f"✅ 成功更新 {success_count}/{len(results)} 个数据源，共新增 {total_inserted} 条数据")
                    if total_inserted > 0:
                        st.balloons()
                else:
                    st.error("❌ 所有数据源更新失败")
        if st.button("🔄 刷新数据显示", key="refresh"):
            st.rerun()


if __name__ == "__main__":
    # 测试代码
    manager = CrawlerManager()
    print("可用数据源:", manager.get_available_sources())

    # 测试THS_724数据源
    result = manager.crawl_incremental_data("同花顺_7*24")
    print("爬取结果:", result)
